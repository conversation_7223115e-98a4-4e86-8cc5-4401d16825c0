#include "ImGuiELGS.h"
#include "aimi_icon.h"
#include <iostream>
#include <unistd.h>
#include <dirent.h>
#include <fcntl.h>
#include <linux/input.h>
#include <cstdlib>
#include <cstring>
#include <chrono>
#include <thread>
#include <stdio.h>
#include <pthread.h>
#include <android/log.h>
#include <signal.h>
#include <limits.h>
#include "wcnm.h"
#include "check.h"
#include "txyun.h"
#define RESET_TEXT "\033[0m"
#define PINK_TEXT "\033[35m"
#define BOLD_TEXT "\033[1m"
#define CRITICAL_PROTECTION __attribute__((optnone)) __attribute__((noinline))                          \
__attribute__((annotate("+fla +indbr ^indbr=3 +icall ^icall=3 +indgv ^indgv=3 +cie ^cie=3 +cfe ^cfe=3 " \
                        "bcf bcf_prob=90 bcf_loop=2 bcf_cond_compl=3 bcf_junkasm "                      \
                        "bcf_junkasm_minnum=2 bcf_junkasm_maxnum=4 strenc strenc_prob=100 "             \
                        "sub sub_prob=70 sub_loop=2 split split_num=3")))
#define OBFUSCATE_FULL CRITICAL_PROTECTION
#define PSEUDO_JUMP
int mode = 0;
int output = 2;
int choice = 0;

int GetEventCount() CRITICAL_PROTECTION
{
    int count = 0;
    dirent *ptr = NULL;
    DIR *dir = opendir("/dev/input/");
    if (dir == NULL)
    {
        std::cerr << "\033[31;1m[-] Failed to open /dev/input/\033[30;1m" << RESET_TEXT << std::endl;
        return -1;
    }
    while ((ptr = readdir(dir)) != NULL)
    {
        if (strstr(ptr->d_name, "event"))
        {
            count++;
        }
    }
    closedir(dir);
    return count ? count : -1;
}

void RunMainLogic() CRITICAL_PROTECTION
{
    ImGuiELGS *ELGS = new ImGuiELGS();
    if (ELGS != nullptr)
    {
        ELGS->ImGuiGetScreenInformation();
        if (!ELGS->ImGuiGetSurfaceWindow())
        {
            delete ELGS;
            return;
        }
        ELGS->ImGuiInItialization();
    }

    input_event event;
    int eventcount = GetEventCount();
    if (eventcount <= 0)
    {
        std::cerr << "\033[31;1m[-] 获取音量监听失败\033[30;1m" << RESET_TEXT << std::endl;
        delete ELGS;
        return;
    }

    int *volumedevicefilearray = (int *)malloc(eventcount * sizeof(int));
    for (int i = 0; i < eventcount; i++)
    {
        char inputfilepath[128] = "";
        sprintf(inputfilepath, "/dev/input/event%d", i);
        volumedevicefilearray[i] = open(inputfilepath, O_RDWR | O_NONBLOCK);
    }

    ELGS->yeaimi_icon = ELGS->texturereadsfile(yeaimi_icon, sizeof(yeaimi_icon));
    ELGS->noaimi_icon = ELGS->texturereadsfile(noaimi_icon, sizeof(noaimi_icon));
    Timer Thread_Time_Synchronization;
    Thread_Time_Synchronization.SetFps(ELGS->OneTimeFrame);
    Thread_Time_Synchronization.AotuFPS_init();

    Thread_Time_Synchronization.SetCPUAffinity();
    while (ELGS->ShutImGuiProcess)
    {
        if (mode == 1)
        {
            for (int i = 0; i < eventcount; i++)
            {
                memset(&event, 0, sizeof(input_event));
                read(volumedevicefilearray[i], &event, sizeof(event));
                if (event.type == EV_KEY && event.value == 1)
                {
                    if (event.code == KEY_VOLUMEUP)
                    {
                        ELGS->ImGuiWindowDisplay = true;
                    }
                    else if (event.code == KEY_VOLUMEDOWN)
                    {
                        ELGS->ImGuiWindowDisplay = false;
                    }
                }
            }
        }

        ELGS->ImGuiWindowStar();
        if (mode == 1)
        {
            ELGS->ImGuiWindowMenu();
        }
        ELGS->ImGuiWindowDraw();
        ELGS->ImGuiWindowExit();

        Thread_Time_Synchronization.SetFps(ELGS->OneTimeFrame);
        Thread_Time_Synchronization.AotuFPS();
    }

    ELGS->ImGuiWindowRele();
    free(volumedevicefilearray);
    delete ELGS;
}
int main(int argc, char *argv[]) CRITICAL_PROTECTION
{
    PSEUDO_JUMP;

    // 测试腾讯云获取功能
    char version[1024] = {0};
    char hash[1024] = {0};
    char download_url[1024] = {0};

    std::cout << BOLD_TEXT << PINK_TEXT << "[+] 正在获取腾讯云分享内容..." << RESET_TEXT << std::endl;
    if (getTxyunVersionHashUrl(TXYUN_URL, version, hash, download_url))
    {
        std::cout << BOLD_TEXT << PINK_TEXT << "[+] 获取成功！" << RESET_TEXT << std::endl;
        std::cout << BOLD_TEXT << PINK_TEXT << "    版本号: " << version << RESET_TEXT << std::endl;
        std::cout << BOLD_TEXT << PINK_TEXT << "    哈希值: " << hash << RESET_TEXT << std::endl;
        std::cout << BOLD_TEXT << PINK_TEXT << "    下载链接: " << download_url << RESET_TEXT << std::endl;
    }
    else
    {
        std::cout << BOLD_TEXT << PINK_TEXT << "[-] 获取腾讯云内容失败" << RESET_TEXT << std::endl;
    }

    if (login())
    {
        std::cout << BOLD_TEXT << PINK_TEXT << " 输1自瞄 输2单透 输完回车键: " << RESET_TEXT;
        std::cin >> mode;
        if (mode == 3)
        {
            std::cout << BOLD_TEXT << PINK_TEXT << "遗憾" << RESET_TEXT << std::endl;
            exit(0);
        }
        std::ifstream inputFile("settings.conf");
        if (inputFile.is_open())
        {
            inputFile >> output >> choice;
            inputFile.close();
            std::cout << BOLD_TEXT << PINK_TEXT << " 遗憾牛逼" << RESET_TEXT << std::endl;
        }
        else
        {
            std::cout << BOLD_TEXT << PINK_TEXT << " 遗憾牛逼" << RESET_TEXT << std::endl;
            std::cout << BOLD_TEXT << PINK_TEXT << " 输1防录屏 输2不开: " << RESET_TEXT;
            std::cin >> output;
            std::cout << BOLD_TEXT << PINK_TEXT << " KSU和AP输1 普通面具输2 输完回车键: " << RESET_TEXT;
            std::cin >> choice;
            std::ofstream outputFile("settings.conf");
            if (outputFile.is_open())
            {
                outputFile << output << " " << choice;
                outputFile.close();
            }
        }
        if (choice == 1)
        {
            system("pm list packages | grep -i 'mt' | sed 's/package://' | while read pkg; do am force-stop $pkg; done");
            pid_t pid = fork();
            if (pid > 0)
            {
                exit(0);
            }
            else if (pid < 0)
            {
                std::cerr << "进程 fork 失败！" << std::endl;
                return 1;
            }
            if (setsid() < 0)
            {
                std::cerr << "创建新会话失败！" << std::endl;
                return 1;
            }
            freopen("/dev/null", "r", stdin);
            freopen("/dev/null", "w", stdout);
            freopen("/dev/null", "w", stderr);
        }

        RunMainLogic();
    }
    else
    {
        exit(0);
    }

    return 0;
}
